import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:guest_posts_buyer/core/models/email_template_model.dart';

enum EmailStatus {
  pending,
  sent,
  failed,
  retrying,
}

class EmailLogModel {
  final String? logId;
  final String toEmail;
  final String? toName;
  final String fromEmail;
  final String fromName;
  final String subject;
  final String htmlContent;
  final String textContent;
  final EmailTemplateType? templateType;
  final String? templateId;
  final EmailStatus status;
  final String? errorMessage;
  final int retryCount;
  final int maxRetries;
  final Map<String, dynamic> templateData;
  final String? relatedOrderId;
  final String? relatedUserId;
  final Timestamp createdAt;
  final Timestamp? sentAt;
  final Timestamp lastUpdated;

  EmailLogModel({
    this.logId,
    required this.toEmail,
    this.toName,
    required this.fromEmail,
    required this.fromName,
    required this.subject,
    required this.htmlContent,
    required this.textContent,
    this.templateType,
    this.templateId,
    this.status = EmailStatus.pending,
    this.errorMessage,
    this.retryCount = 0,
    this.maxRetries = 3,
    this.templateData = const {},
    this.relatedOrderId,
    this.relatedUserId,
    required this.createdAt,
    this.sentAt,
    required this.lastUpdated,
  });

  factory EmailLogModel.fromMap(Map<String, dynamic> map) {
    return EmailLogModel(
      logId: map['logId'] as String?,
      toEmail: map['toEmail'] as String? ?? '',
      toName: map['toName'] as String?,
      fromEmail: map['fromEmail'] as String? ?? '',
      fromName: map['fromName'] as String? ?? '',
      subject: map['subject'] as String? ?? '',
      htmlContent: map['htmlContent'] as String? ?? '',
      textContent: map['textContent'] as String? ?? '',
      templateType: map['templateType'] != null
          ? EmailTemplateType.values.firstWhere(
              (e) => e.toString() == map['templateType'],
              orElse: () => EmailTemplateType.orderConfirmation,
            )
          : null,
      templateId: map['templateId'] as String?,
      status: EmailStatus.values.firstWhere(
        (e) => e.toString() == map['status'],
        orElse: () => EmailStatus.pending,
      ),
      errorMessage: map['errorMessage'] as String?,
      retryCount: map['retryCount'] as int? ?? 0,
      maxRetries: map['maxRetries'] as int? ?? 3,
      templateData: Map<String, dynamic>.from(map['templateData'] ?? {}),
      relatedOrderId: map['relatedOrderId'] as String?,
      relatedUserId: map['relatedUserId'] as String?,
      createdAt: map['createdAt'] as Timestamp? ?? Timestamp.now(),
      sentAt: map['sentAt'] as Timestamp?,
      lastUpdated: map['lastUpdated'] as Timestamp? ?? Timestamp.now(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'logId': logId,
      'toEmail': toEmail,
      'toName': toName,
      'fromEmail': fromEmail,
      'fromName': fromName,
      'subject': subject,
      'htmlContent': htmlContent,
      'textContent': textContent,
      'templateType': templateType?.toString(),
      'templateId': templateId,
      'status': status.toString(),
      'errorMessage': errorMessage,
      'retryCount': retryCount,
      'maxRetries': maxRetries,
      'templateData': templateData,
      'relatedOrderId': relatedOrderId,
      'relatedUserId': relatedUserId,
      'createdAt': createdAt,
      'sentAt': sentAt,
      'lastUpdated': lastUpdated,
    };
  }

  EmailLogModel copyWith({
    String? logId,
    String? toEmail,
    String? toName,
    String? fromEmail,
    String? fromName,
    String? subject,
    String? htmlContent,
    String? textContent,
    EmailTemplateType? templateType,
    String? templateId,
    EmailStatus? status,
    String? errorMessage,
    int? retryCount,
    int? maxRetries,
    Map<String, dynamic>? templateData,
    String? relatedOrderId,
    String? relatedUserId,
    Timestamp? createdAt,
    Timestamp? sentAt,
    Timestamp? lastUpdated,
  }) {
    return EmailLogModel(
      logId: logId ?? this.logId,
      toEmail: toEmail ?? this.toEmail,
      toName: toName ?? this.toName,
      fromEmail: fromEmail ?? this.fromEmail,
      fromName: fromName ?? this.fromName,
      subject: subject ?? this.subject,
      htmlContent: htmlContent ?? this.htmlContent,
      textContent: textContent ?? this.textContent,
      templateType: templateType ?? this.templateType,
      templateId: templateId ?? this.templateId,
      status: status ?? this.status,
      errorMessage: errorMessage ?? this.errorMessage,
      retryCount: retryCount ?? this.retryCount,
      maxRetries: maxRetries ?? this.maxRetries,
      templateData: templateData ?? this.templateData,
      relatedOrderId: relatedOrderId ?? this.relatedOrderId,
      relatedUserId: relatedUserId ?? this.relatedUserId,
      createdAt: createdAt ?? this.createdAt,
      sentAt: sentAt ?? this.sentAt,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  bool get canRetry => retryCount < maxRetries && status == EmailStatus.failed;
  bool get isSuccessful => status == EmailStatus.sent;
  bool get isPending => status == EmailStatus.pending || status == EmailStatus.retrying;
}
