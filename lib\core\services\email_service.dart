import 'dart:async';
import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:mailer/mailer.dart';
import 'package:mailer/smtp_server.dart';
import 'package:guest_posts_buyer/core/models/email_config_model.dart';
import 'package:guest_posts_buyer/core/models/email_template_model.dart';
import 'package:guest_posts_buyer/core/models/email_log_model.dart';
import 'package:guest_posts_buyer/core/services/cRUD_services.dart';

class EmailService {
  static final EmailService _instance = EmailService._internal();
  factory EmailService() => _instance;
  EmailService._internal();

  final FirestoreService _firestoreService = FirestoreService();
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  
  EmailConfigModel? _currentConfig;
  Map<EmailTemplateType, EmailTemplateModel> _templates = {};
  Timer? _retryTimer;

  // Initialize the email service
  Future<void> initialize() async {
    await _loadEmailConfig();
    await _loadEmailTemplates();
    _startRetryTimer();
  }

  // Load email configuration from Firestore
  Future<void> _loadEmailConfig() async {
    try {
      final environment = kDebugMode ? 'development' : 'production';
      final querySnapshot = await _firestore
          .collection('email_configs')
          .where('environment', isEqualTo: environment)
          .where('isActive', isEqualTo: true)
          .limit(1)
          .get();

      if (querySnapshot.docs.isNotEmpty) {
        final doc = querySnapshot.docs.first;
        _currentConfig = EmailConfigModel.fromMap(
          doc.data()..['configId'] = doc.id,
        );
      }
    } catch (e) {
      print('Error loading email config: $e');
    }
  }

  // Load email templates from Firestore
  Future<void> _loadEmailTemplates() async {
    try {
      final querySnapshot = await _firestore
          .collection('email_templates')
          .where('isActive', isEqualTo: true)
          .get();

      _templates.clear();
      for (final doc in querySnapshot.docs) {
        final template = EmailTemplateModel.fromMap(
          doc.data()..['templateId'] = doc.id,
        );
        _templates[template.type] = template;
      }
    } catch (e) {
      print('Error loading email templates: $e');
    }
  }

  // Send email using template
  Future<bool> sendTemplateEmail({
    required EmailTemplateType templateType,
    required String toEmail,
    String? toName,
    required Map<String, dynamic> templateData,
    String? relatedOrderId,
    String? relatedUserId,
  }) async {
    try {
      if (_currentConfig == null) {
        print('Email configuration not loaded');
        return false;
      }

      final template = _templates[templateType];
      if (template == null) {
        print('Email template not found for type: $templateType');
        return false;
      }

      // Process template content
      final subject = template.getProcessedSubject(templateData);
      final htmlContent = template.getProcessedHtmlContent(templateData);
      final textContent = template.getProcessedTextContent(templateData);

      // Create email log entry
      final emailLog = EmailLogModel(
        toEmail: toEmail,
        toName: toName,
        fromEmail: _currentConfig!.fromEmail,
        fromName: _currentConfig!.fromName,
        subject: subject,
        htmlContent: htmlContent,
        textContent: textContent,
        templateType: templateType,
        templateId: template.templateId,
        templateData: templateData,
        relatedOrderId: relatedOrderId,
        relatedUserId: relatedUserId,
        createdAt: Timestamp.now(),
        lastUpdated: Timestamp.now(),
      );

      // Save to email logs
      final logId = await _saveEmailLog(emailLog);
      
      // Send the email
      return await _sendEmail(emailLog.copyWith(logId: logId));
    } catch (e) {
      print('Error sending template email: $e');
      return false;
    }
  }

  // Send custom email
  Future<bool> sendCustomEmail({
    required String toEmail,
    String? toName,
    required String subject,
    required String htmlContent,
    String? textContent,
    String? relatedOrderId,
    String? relatedUserId,
  }) async {
    try {
      if (_currentConfig == null) {
        print('Email configuration not loaded');
        return false;
      }

      final emailLog = EmailLogModel(
        toEmail: toEmail,
        toName: toName,
        fromEmail: _currentConfig!.fromEmail,
        fromName: _currentConfig!.fromName,
        subject: subject,
        htmlContent: htmlContent,
        textContent: textContent ?? htmlContent,
        relatedOrderId: relatedOrderId,
        relatedUserId: relatedUserId,
        createdAt: Timestamp.now(),
        lastUpdated: Timestamp.now(),
      );

      // Save to email logs
      final logId = await _saveEmailLog(emailLog);
      
      // Send the email
      return await _sendEmail(emailLog.copyWith(logId: logId));
    } catch (e) {
      print('Error sending custom email: $e');
      return false;
    }
  }

  // Internal method to send email
  Future<bool> _sendEmail(EmailLogModel emailLog) async {
    try {
      if (_currentConfig == null) return false;

      // Create SMTP server configuration
      final smtpServer = SmtpServer(
        _currentConfig!.smtpHost,
        port: _currentConfig!.smtpPort,
        username: _currentConfig!.username,
        password: _currentConfig!.password,
        ssl: _currentConfig!.useSSL,
        allowInsecure: !_currentConfig!.useTLS,
      );

      // Create message
      final message = Message()
        ..from = Address(_currentConfig!.fromEmail, _currentConfig!.fromName)
        ..recipients.add(Address(emailLog.toEmail, emailLog.toName))
        ..subject = emailLog.subject
        ..html = emailLog.htmlContent
        ..text = emailLog.textContent;

      // Send email
      await send(message, smtpServer);

      // Update email log as sent
      await _updateEmailLogStatus(
        emailLog.logId!,
        EmailStatus.sent,
        sentAt: Timestamp.now(),
      );

      return true;
    } catch (e) {
      print('Error sending email: $e');
      
      // Update email log as failed
      await _updateEmailLogStatus(
        emailLog.logId!,
        EmailStatus.failed,
        errorMessage: e.toString(),
        retryCount: emailLog.retryCount + 1,
      );

      return false;
    }
  }

  // Save email log to Firestore
  Future<String> _saveEmailLog(EmailLogModel emailLog) async {
    return await _firestoreService.addDocument(
      collectionPath: 'email_logs',
      data: emailLog.toMap(),
    );
  }

  // Update email log status
  Future<void> _updateEmailLogStatus(
    String logId,
    EmailStatus status, {
    String? errorMessage,
    int? retryCount,
    Timestamp? sentAt,
  }) async {
    final updateData = <String, dynamic>{
      'status': status.toString(),
      'lastUpdated': FieldValue.serverTimestamp(),
    };

    if (errorMessage != null) updateData['errorMessage'] = errorMessage;
    if (retryCount != null) updateData['retryCount'] = retryCount;
    if (sentAt != null) updateData['sentAt'] = sentAt;

    await _firestoreService.updateDocument(
      collectionPath: 'email_logs',
      documentId: logId,
      data: updateData,
    );
  }

  // Start retry timer for failed emails
  void _startRetryTimer() {
    _retryTimer?.cancel();
    _retryTimer = Timer.periodic(const Duration(minutes: 5), (timer) {
      _retryFailedEmails();
    });
  }

  // Retry failed emails
  Future<void> _retryFailedEmails() async {
    try {
      final querySnapshot = await _firestore
          .collection('email_logs')
          .where('status', isEqualTo: EmailStatus.failed.toString())
          .where('retryCount', isLessThan: 3)
          .limit(10)
          .get();

      for (final doc in querySnapshot.docs) {
        final emailLog = EmailLogModel.fromMap(
          doc.data()..['logId'] = doc.id,
        );

        if (emailLog.canRetry) {
          // Update status to retrying
          await _updateEmailLogStatus(emailLog.logId!, EmailStatus.retrying);
          
          // Attempt to send again
          await _sendEmail(emailLog);
        }
      }
    } catch (e) {
      print('Error retrying failed emails: $e');
    }
  }

  // Dispose resources
  void dispose() {
    _retryTimer?.cancel();
  }

  // Getters
  bool get isConfigured => _currentConfig != null;
  EmailConfigModel? get currentConfig => _currentConfig;
  Map<EmailTemplateType, EmailTemplateModel> get templates => _templates;
}
