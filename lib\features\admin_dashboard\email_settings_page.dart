import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:guest_posts_buyer/core/models/email_config_model.dart';
import 'package:guest_posts_buyer/core/services/cRUD_services.dart';
import 'package:guest_posts_buyer/core/utils/colors.dart';
import 'package:guest_posts_buyer/core/services/email_service.dart';

class EmailSettingsPage extends StatefulWidget {
  const EmailSettingsPage({super.key});

  @override
  State<EmailSettingsPage> createState() => _EmailSettingsPageState();
}

class _EmailSettingsPageState extends State<EmailSettingsPage> {
  final FirestoreService _firestoreService = FirestoreService();
  final EmailService _emailService = EmailService();
  
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = false;
  bool _isSaving = false;
  bool _isTesting = false;
  
  // Form controllers
  final _smtpHostController = TextEditingController();
  final _smtpPortController = TextEditingController();
  final _usernameController = TextEditingController();
  final _passwordController = TextEditingController();
  final _fromEmailController = TextEditingController();
  final _fromNameController = TextEditingController();
  final _testEmailController = TextEditingController();
  
  bool _useTLS = true;
  bool _useSSL = false;
  String _selectedProvider = 'custom';
  String _environment = 'development';
  
  EmailConfigModel? _currentConfig;

  @override
  void initState() {
    super.initState();
    _loadEmailConfig();
  }

  @override
  void dispose() {
    _smtpHostController.dispose();
    _smtpPortController.dispose();
    _usernameController.dispose();
    _passwordController.dispose();
    _fromEmailController.dispose();
    _fromNameController.dispose();
    _testEmailController.dispose();
    super.dispose();
  }

  Future<void> _loadEmailConfig() async {
    setState(() => _isLoading = true);
    
    try {
      final querySnapshot = await FirebaseFirestore.instance
          .collection('email_configs')
          .where('environment', isEqualTo: _environment)
          .where('isActive', isEqualTo: true)
          .limit(1)
          .get();

      if (querySnapshot.docs.isNotEmpty) {
        final doc = querySnapshot.docs.first;
        _currentConfig = EmailConfigModel.fromMap(
          doc.data()..['configId'] = doc.id,
        );
        _populateForm();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading email config: $e'),
            backgroundColor: AppColors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  void _populateForm() {
    if (_currentConfig != null) {
      _smtpHostController.text = _currentConfig!.smtpHost;
      _smtpPortController.text = _currentConfig!.smtpPort.toString();
      _usernameController.text = _currentConfig!.username;
      _passwordController.text = _currentConfig!.password;
      _fromEmailController.text = _currentConfig!.fromEmail;
      _fromNameController.text = _currentConfig!.fromName;
      _useTLS = _currentConfig!.useTLS;
      _useSSL = _currentConfig!.useSSL;
    }
  }

  void _setProviderDefaults(String provider) {
    setState(() {
      _selectedProvider = provider;
      switch (provider) {
        case 'gmail':
          _smtpHostController.text = 'smtp.gmail.com';
          _smtpPortController.text = '587';
          _useTLS = true;
          _useSSL = false;
          break;
        case 'sendgrid':
          _smtpHostController.text = 'smtp.sendgrid.net';
          _smtpPortController.text = '587';
          _useTLS = true;
          _useSSL = false;
          break;
        case 'outlook':
          _smtpHostController.text = 'smtp-mail.outlook.com';
          _smtpPortController.text = '587';
          _useTLS = true;
          _useSSL = false;
          break;
        default:
          // Custom - don't change anything
          break;
      }
    });
  }

  Future<void> _saveConfig() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isSaving = true);

    try {
      final config = EmailConfigModel(
        configId: _currentConfig?.configId,
        smtpHost: _smtpHostController.text.trim(),
        smtpPort: int.parse(_smtpPortController.text.trim()),
        username: _usernameController.text.trim(),
        password: _passwordController.text.trim(),
        fromEmail: _fromEmailController.text.trim(),
        fromName: _fromNameController.text.trim(),
        useTLS: _useTLS,
        useSSL: _useSSL,
        environment: _environment,
        createdAt: _currentConfig?.createdAt ?? Timestamp.now(),
        lastUpdated: Timestamp.now(),
      );

      if (_currentConfig?.configId != null) {
        await _firestoreService.updateDocument(
          collectionPath: 'email_configs',
          documentId: _currentConfig!.configId!,
          data: config.toMap(),
        );
      } else {
        // Deactivate existing configs for this environment
        final existingConfigs = await FirebaseFirestore.instance
            .collection('email_configs')
            .where('environment', isEqualTo: _environment)
            .get();
        
        for (final doc in existingConfigs.docs) {
          await _firestoreService.updateDocument(
            collectionPath: 'email_configs',
            documentId: doc.id,
            data: {'isActive': false},
          );
        }

        // Add new config
        final configId = await _firestoreService.addDocument(
          collectionPath: 'email_configs',
          data: config.toMap(),
        );
        
        _currentConfig = config.copyWith(configId: configId);
      }

      // Reinitialize email service
      await _emailService.initialize();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Email configuration saved successfully'),
            backgroundColor: AppColors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving configuration: $e'),
            backgroundColor: AppColors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isSaving = false);
      }
    }
  }

  Future<void> _testEmailConfig() async {
    if (!_formKey.currentState!.validate()) return;
    if (_testEmailController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please enter a test email address'),
          backgroundColor: AppColors.red,
        ),
      );
      return;
    }

    setState(() => _isTesting = true);

    try {
      final success = await _emailService.sendCustomEmail(
        toEmail: _testEmailController.text.trim(),
        subject: 'Test Email Configuration',
        htmlContent: '''
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Test Email</title>
</head>
<body>
    <h2>Email Configuration Test</h2>
    <p>This is a test email to verify your SMTP configuration is working correctly.</p>
    <p>If you received this email, your configuration is successful!</p>
    <p>Sent at: ${DateTime.now()}</p>
</body>
</html>
        ''',
        textContent: '''
Email Configuration Test

This is a test email to verify your SMTP configuration is working correctly.
If you received this email, your configuration is successful!

Sent at: ${DateTime.now()}
        ''',
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(success 
              ? 'Test email sent successfully!' 
              : 'Failed to send test email. Please check your configuration.'
            ),
            backgroundColor: success ? AppColors.green : AppColors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error testing email: $e'),
            backgroundColor: AppColors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isTesting = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    return Scaffold(
      backgroundColor: AppColors.backgroundColor,
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: const Color.fromRGBO(26, 115, 232, 0.08),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: AppColors.blueLightest,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Icon(
                      Icons.email_outlined,
                      color: AppColors.blue,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  const Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Email Configuration',
                          style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: AppColors.dark,
                          ),
                        ),
                        SizedBox(height: 4),
                        Text(
                          'Configure SMTP settings for automated email notifications',
                          style: TextStyle(
                            fontSize: 14,
                            color: AppColors.grey,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Configuration Form
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: const Color.fromRGBO(26, 115, 232, 0.08),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Environment Selection
                    Row(
                      children: [
                        const Text(
                          'Environment:',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: AppColors.dark,
                          ),
                        ),
                        const SizedBox(width: 16),
                        DropdownButton<String>(
                          value: _environment,
                          onChanged: (value) {
                            if (value != null) {
                              setState(() => _environment = value);
                              _loadEmailConfig();
                            }
                          },
                          items: const [
                            DropdownMenuItem(
                              value: 'development',
                              child: Text('Development'),
                            ),
                            DropdownMenuItem(
                              value: 'production',
                              child: Text('Production'),
                            ),
                          ],
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: 24),
                    
                    // Provider Selection
                    const Text(
                      'Email Provider',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: AppColors.dark,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Wrap(
                      spacing: 8,
                      children: [
                        _buildProviderChip('Gmail', 'gmail'),
                        _buildProviderChip('SendGrid', 'sendgrid'),
                        _buildProviderChip('Outlook', 'outlook'),
                        _buildProviderChip('Custom', 'custom'),
                      ],
                    ),
                    
                    const SizedBox(height: 24),
                    
                    // SMTP Configuration
                    Row(
                      children: [
                        Expanded(
                          flex: 2,
                          child: TextFormField(
                            controller: _smtpHostController,
                            decoration: const InputDecoration(
                              labelText: 'SMTP Host',
                              border: OutlineInputBorder(),
                            ),
                            validator: (value) {
                              if (value?.isEmpty ?? true) {
                                return 'SMTP Host is required';
                              }
                              return null;
                            },
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: TextFormField(
                            controller: _smtpPortController,
                            decoration: const InputDecoration(
                              labelText: 'Port',
                              border: OutlineInputBorder(),
                            ),
                            keyboardType: TextInputType.number,
                            validator: (value) {
                              if (value?.isEmpty ?? true) {
                                return 'Port is required';
                              }
                              final port = int.tryParse(value!);
                              if (port == null || port <= 0 || port > 65535) {
                                return 'Invalid port number';
                              }
                              return null;
                            },
                          ),
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // Authentication
                    Row(
                      children: [
                        Expanded(
                          child: TextFormField(
                            controller: _usernameController,
                            decoration: const InputDecoration(
                              labelText: 'Username',
                              border: OutlineInputBorder(),
                            ),
                            validator: (value) {
                              if (value?.isEmpty ?? true) {
                                return 'Username is required';
                              }
                              return null;
                            },
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: TextFormField(
                            controller: _passwordController,
                            decoration: const InputDecoration(
                              labelText: 'Password',
                              border: OutlineInputBorder(),
                            ),
                            obscureText: true,
                            validator: (value) {
                              if (value?.isEmpty ?? true) {
                                return 'Password is required';
                              }
                              return null;
                            },
                          ),
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // From Email Configuration
                    Row(
                      children: [
                        Expanded(
                          child: TextFormField(
                            controller: _fromEmailController,
                            decoration: const InputDecoration(
                              labelText: 'From Email',
                              border: OutlineInputBorder(),
                            ),
                            validator: (value) {
                              if (value?.isEmpty ?? true) {
                                return 'From Email is required';
                              }
                              if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value!)) {
                                return 'Invalid email format';
                              }
                              return null;
                            },
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: TextFormField(
                            controller: _fromNameController,
                            decoration: const InputDecoration(
                              labelText: 'From Name',
                              border: OutlineInputBorder(),
                            ),
                            validator: (value) {
                              if (value?.isEmpty ?? true) {
                                return 'From Name is required';
                              }
                              return null;
                            },
                          ),
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // Security Options
                    Row(
                      children: [
                        Expanded(
                          child: CheckboxListTile(
                            title: const Text('Use TLS'),
                            value: _useTLS,
                            onChanged: (value) {
                              setState(() => _useTLS = value ?? false);
                            },
                            controlAffinity: ListTileControlAffinity.leading,
                          ),
                        ),
                        Expanded(
                          child: CheckboxListTile(
                            title: const Text('Use SSL'),
                            value: _useSSL,
                            onChanged: (value) {
                              setState(() => _useSSL = value ?? false);
                            },
                            controlAffinity: ListTileControlAffinity.leading,
                          ),
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: 24),
                    
                    // Test Email Section
                    const Divider(),
                    const SizedBox(height: 16),
                    
                    const Text(
                      'Test Configuration',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: AppColors.dark,
                      ),
                    ),
                    const SizedBox(height: 8),
                    
                    Row(
                      children: [
                        Expanded(
                          child: TextFormField(
                            controller: _testEmailController,
                            decoration: const InputDecoration(
                              labelText: 'Test Email Address',
                              border: OutlineInputBorder(),
                              hintText: 'Enter email to send test message',
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        ElevatedButton.icon(
                          onPressed: _isTesting ? null : _testEmailConfig,
                          icon: _isTesting 
                            ? const SizedBox(
                                width: 16,
                                height: 16,
                                child: CircularProgressIndicator(strokeWidth: 2),
                              )
                            : const Icon(Icons.send),
                          label: Text(_isTesting ? 'Testing...' : 'Test'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.blue,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(
                              horizontal: 24,
                              vertical: 12,
                            ),
                          ),
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: 24),
                    
                    // Save Button
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: _isSaving ? null : _saveConfig,
                        icon: _isSaving 
                          ? const SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                          : const Icon(Icons.save),
                        label: Text(_isSaving ? 'Saving...' : 'Save Configuration'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.blue,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProviderChip(String label, String value) {
    final isSelected = _selectedProvider == value;
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        if (selected) {
          _setProviderDefaults(value);
        }
      },
      selectedColor: AppColors.blueLightest,
      checkmarkColor: AppColors.blue,
    );
  }
}
