import 'package:cloud_firestore/cloud_firestore.dart';

enum EmailTemplateType {
  orderConfirmation,
  orderStatusUpdate,
  publisherNotification,
  adminNotification,
  paymentConfirmation,
  userRegistration,
  emailVerification,
  passwordReset,
  withdrawalRequest,
  withdrawalApproval,
  disputeNotification,
}

class EmailTemplateModel {
  final String? templateId;
  final EmailTemplateType type;
  final String name;
  final String subject;
  final String htmlContent;
  final String textContent;
  final Map<String, String> variables; // Available template variables
  final bool isActive;
  final String language;
  final Timestamp createdAt;
  final Timestamp lastUpdated;

  EmailTemplateModel({
    this.templateId,
    required this.type,
    required this.name,
    required this.subject,
    required this.htmlContent,
    required this.textContent,
    this.variables = const {},
    this.isActive = true,
    this.language = 'en',
    required this.createdAt,
    required this.lastUpdated,
  });

  factory EmailTemplateModel.fromMap(Map<String, dynamic> map) {
    return EmailTemplateModel(
      templateId: map['templateId'] as String?,
      type: EmailTemplateType.values.firstWhere(
        (e) => e.toString() == map['type'],
        orElse: () => EmailTemplateType.orderConfirmation,
      ),
      name: map['name'] as String? ?? '',
      subject: map['subject'] as String? ?? '',
      htmlContent: map['htmlContent'] as String? ?? '',
      textContent: map['textContent'] as String? ?? '',
      variables: Map<String, String>.from(map['variables'] ?? {}),
      isActive: map['isActive'] as bool? ?? true,
      language: map['language'] as String? ?? 'en',
      createdAt: map['createdAt'] as Timestamp? ?? Timestamp.now(),
      lastUpdated: map['lastUpdated'] as Timestamp? ?? Timestamp.now(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'templateId': templateId,
      'type': type.toString(),
      'name': name,
      'subject': subject,
      'htmlContent': htmlContent,
      'textContent': textContent,
      'variables': variables,
      'isActive': isActive,
      'language': language,
      'createdAt': createdAt,
      'lastUpdated': lastUpdated,
    };
  }

  EmailTemplateModel copyWith({
    String? templateId,
    EmailTemplateType? type,
    String? name,
    String? subject,
    String? htmlContent,
    String? textContent,
    Map<String, String>? variables,
    bool? isActive,
    String? language,
    Timestamp? createdAt,
    Timestamp? lastUpdated,
  }) {
    return EmailTemplateModel(
      templateId: templateId ?? this.templateId,
      type: type ?? this.type,
      name: name ?? this.name,
      subject: subject ?? this.subject,
      htmlContent: htmlContent ?? this.htmlContent,
      textContent: textContent ?? this.textContent,
      variables: variables ?? this.variables,
      isActive: isActive ?? this.isActive,
      language: language ?? this.language,
      createdAt: createdAt ?? this.createdAt,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  // Helper method to replace variables in content
  String processContent(String content, Map<String, dynamic> data) {
    String processedContent = content;
    
    for (String variable in variables.keys) {
      final placeholder = '{{$variable}}';
      final value = data[variable]?.toString() ?? '';
      processedContent = processedContent.replaceAll(placeholder, value);
    }
    
    return processedContent;
  }

  String getProcessedSubject(Map<String, dynamic> data) {
    return processContent(subject, data);
  }

  String getProcessedHtmlContent(Map<String, dynamic> data) {
    return processContent(htmlContent, data);
  }

  String getProcessedTextContent(Map<String, dynamic> data) {
    return processContent(textContent, data);
  }
}
