import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:guest_posts_buyer/core/models/order_model.dart';
import 'package:guest_posts_buyer/core/models/user_model.dart';
import 'package:guest_posts_buyer/core/models/website_model.dart';
import 'package:guest_posts_buyer/core/models/email_template_model.dart';
import 'package:guest_posts_buyer/core/services/email_service.dart';
import 'package:guest_posts_buyer/core/services/cRUD_services.dart';
import 'package:intl/intl.dart';

class EmailIntegrationService {
  static final EmailIntegrationService _instance = EmailIntegrationService._internal();
  factory EmailIntegrationService() => _instance;
  EmailIntegrationService._internal();

  final EmailService _emailService = EmailService();
  final FirestoreService _firestoreService = FirestoreService();

  // Send order confirmation email
  Future<bool> sendOrderConfirmationEmail(OrderModel order) async {
    try {
      // Get buyer details
      final buyerData = await _firestoreService.getDocument(
        collectionPath: 'users',
        documentId: order.buyerId,
      );

      if (buyerData == null) return false;

      final buyer = UserModel.fromMap(buyerData, order.buyerId);
      
      // Get website details
      final websiteData = await _firestoreService.getDocument(
        collectionPath: 'websites',
        documentId: order.websiteId,
      );

      final templateData = {
        'buyerName': buyer.name,
        'buyerEmail': buyer.email,
        'orderNumber': order.orderId ?? 'N/A',
        'websiteUrl': order.websiteUrl,
        'postTitle': order.postTitle,
        'wordCount': order.wordCount.toString(),
        'totalPrice': order.totalPrice.toStringAsFixed(2),
        'orderDate': DateFormat('MMM dd, yyyy').format(order.orderDate.toDate()),
        'backlinkType': order.backlinkType,
        'isSponsored': order.isSponsored ? 'Yes' : 'No',
      };

      return await _emailService.sendTemplateEmail(
        templateType: EmailTemplateType.orderConfirmation,
        toEmail: buyer.email,
        toName: buyer.name,
        templateData: templateData,
        relatedOrderId: order.orderId,
        relatedUserId: order.buyerId,
      );
    } catch (e) {
      print('Error sending order confirmation email: $e');
      return false;
    }
  }

  // Send order status update email
  Future<bool> sendOrderStatusUpdateEmail(
    OrderModel order,
    String previousStatus,
    String? notes,
  ) async {
    try {
      // Get buyer details
      final buyerData = await _firestoreService.getDocument(
        collectionPath: 'users',
        documentId: order.buyerId,
      );

      if (buyerData == null) return false;

      final buyer = UserModel.fromMap(buyerData, order.buyerId);

      final templateData = {
        'buyerName': buyer.name,
        'buyerEmail': buyer.email,
        'orderNumber': order.orderId ?? 'N/A',
        'status': order.status,
        'statusDetail': order.statusDetail ?? '',
        'previousStatus': previousStatus,
        'updateDate': DateFormat('MMM dd, yyyy HH:mm').format(
          order.lastUpdated?.toDate() ?? DateTime.now(),
        ),
        'notes': notes ?? '',
        'websiteUrl': order.websiteUrl,
        'postTitle': order.postTitle,
      };

      return await _emailService.sendTemplateEmail(
        templateType: EmailTemplateType.orderStatusUpdate,
        toEmail: buyer.email,
        toName: buyer.name,
        templateData: templateData,
        relatedOrderId: order.orderId,
        relatedUserId: order.buyerId,
      );
    } catch (e) {
      print('Error sending order status update email: $e');
      return false;
    }
  }

  // Send publisher notification email
  Future<bool> sendPublisherNotificationEmail(
    OrderModel order,
    String publisherId,
  ) async {
    try {
      // Get publisher details
      final publisherData = await _firestoreService.getDocument(
        collectionPath: 'users',
        documentId: publisherId,
      );

      if (publisherData == null) return false;

      final publisher = UserModel.fromMap(publisherData, publisherId);

      final templateData = {
        'publisherName': publisher.name,
        'publisherEmail': publisher.email,
        'orderNumber': order.orderId ?? 'N/A',
        'websiteUrl': order.websiteUrl,
        'postTitle': order.postTitle,
        'wordCount': order.wordCount.toString(),
        'backlinkType': order.backlinkType,
        'isSponsored': order.isSponsored ? 'Yes' : 'No',
        'orderDate': DateFormat('MMM dd, yyyy').format(order.orderDate.toDate()),
        'totalPrice': order.totalPrice.toStringAsFixed(2),
      };

      return await _emailService.sendTemplateEmail(
        templateType: EmailTemplateType.publisherNotification,
        toEmail: publisher.email,
        toName: publisher.name,
        templateData: templateData,
        relatedOrderId: order.orderId,
        relatedUserId: publisherId,
      );
    } catch (e) {
      print('Error sending publisher notification email: $e');
      return false;
    }
  }

  // Send admin notification email
  Future<bool> sendAdminNotificationEmail(
    String subject,
    String message,
    {String? relatedOrderId, String? relatedUserId}
  ) async {
    try {
      // Get admin email from settings
      final settingsDoc = await _firestoreService.getDocument(
        collectionPath: 'settings',
        documentId: 'defaults',
      );

      final adminEmail = settingsDoc?['adminEmail'] as String? ?? '<EMAIL>';
      final adminName = settingsDoc?['adminName'] as String? ?? 'Admin';

      return await _emailService.sendCustomEmail(
        toEmail: adminEmail,
        toName: adminName,
        subject: subject,
        htmlContent: '''
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Admin Notification</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #1A73E8; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; background: #f9f9f9; }
        .message { background: white; padding: 15px; margin: 15px 0; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Admin Notification</h1>
        </div>
        <div class="content">
            <div class="message">
                <h3>$subject</h3>
                <p>$message</p>
                <p><strong>Time:</strong> ${DateFormat('MMM dd, yyyy HH:mm').format(DateTime.now())}</p>
            </div>
        </div>
    </div>
</body>
</html>
        ''',
        textContent: '''
Admin Notification

$subject

$message

Time: ${DateFormat('MMM dd, yyyy HH:mm').format(DateTime.now())}
        ''',
        relatedOrderId: relatedOrderId,
        relatedUserId: relatedUserId,
      );
    } catch (e) {
      print('Error sending admin notification email: $e');
      return false;
    }
  }

  // Send payment confirmation email
  Future<bool> sendPaymentConfirmationEmail(
    String userId,
    double amount,
    String paymentId,
    String? orderId,
  ) async {
    try {
      // Get user details
      final userData = await _firestoreService.getDocument(
        collectionPath: 'users',
        documentId: userId,
      );

      if (userData == null) return false;

      final user = UserModel.fromMap(userData, userId);

      final templateData = {
        'userName': user.name,
        'userEmail': user.email,
        'amount': amount.toStringAsFixed(2),
        'paymentId': paymentId,
        'orderId': orderId ?? 'N/A',
        'paymentDate': DateFormat('MMM dd, yyyy HH:mm').format(DateTime.now()),
      };

      return await _emailService.sendTemplateEmail(
        templateType: EmailTemplateType.paymentConfirmation,
        toEmail: user.email,
        toName: user.name,
        templateData: templateData,
        relatedOrderId: orderId,
        relatedUserId: userId,
      );
    } catch (e) {
      print('Error sending payment confirmation email: $e');
      return false;
    }
  }

  // Send user registration email
  Future<bool> sendUserRegistrationEmail(UserModel user) async {
    try {
      final templateData = {
        'userName': user.name,
        'userEmail': user.email,
        'registrationDate': DateFormat('MMM dd, yyyy').format(DateTime.now()),
        'userType': user.isPublisher ? 'Publisher' : 'Buyer',
      };

      return await _emailService.sendTemplateEmail(
        templateType: EmailTemplateType.userRegistration,
        toEmail: user.email,
        toName: user.name,
        templateData: templateData,
        relatedUserId: user.uid,
      );
    } catch (e) {
      print('Error sending user registration email: $e');
      return false;
    }
  }

  // Send email verification
  Future<bool> sendEmailVerificationEmail(String email, String verificationLink) async {
    try {
      final templateData = {
        'email': email,
        'verificationLink': verificationLink,
      };

      return await _emailService.sendTemplateEmail(
        templateType: EmailTemplateType.emailVerification,
        toEmail: email,
        templateData: templateData,
      );
    } catch (e) {
      print('Error sending email verification: $e');
      return false;
    }
  }
}

