import 'dart:convert';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:guest_posts_buyer/core/services/cRUD_services.dart';
import 'package:guest_posts_buyer/core/services/email_integration_service.dart';
import 'package:guest_posts_buyer/core/models/user_model.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class AuthService {
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static final GoogleSignIn _googleSignIn = GoogleSignIn();
  static final FirestoreService _firestoreService = FirestoreService();
  static final EmailIntegrationService _emailService =
      EmailIntegrationService();

  // SharedPreferences keys
  static const String _userDataKey = 'userData';

  bool get isLoggedIn => _auth.currentUser != null;

  User? get currentUser => _auth.currentUser;

  /// Get SharedPreferences instance
  Future<SharedPreferences> _getPrefs() async {
    return await SharedPreferences.getInstance();
  }

  /// Convert Timestamp to serializable format
  dynamic _convertTimestamp(dynamic value) {
    if (value is Timestamp) {
      return value.toDate().toIso8601String();
    }
    return value;
  }

  /// Convert user data for JSON serialization
  Map<String, dynamic> _prepareForJson(Map<String, dynamic> userData) {
    return userData.map((key, value) {
      if (value is Map) {
        return MapEntry(key, _prepareForJson(value.cast<String, dynamic>()));
      }
      return MapEntry(key, _convertTimestamp(value));
    });
  }

  /// Save user data to SharedPreferences
  Future<void> _saveUserDataToPrefs(Map<String, dynamic> userData) async {
    final prefs = await _getPrefs();
    final jsonReadyData = _prepareForJson(userData);
    await prefs.setString(_userDataKey, jsonEncode(jsonReadyData));
  }

  /// Get user data from SharedPreferences
  Future<Map<String, dynamic>?> _getUserDataFromPrefs() async {
    final prefs = await _getPrefs();
    final userDataString = prefs.getString(_userDataKey);
    if (userDataString != null) {
      return jsonDecode(userDataString) as Map<String, dynamic>;
    }
    return null;
  }

  /// Clear user data from SharedPreferences
  Future<void> _clearUserDataFromPrefs() async {
    final prefs = await _getPrefs();
    await prefs.remove(_userDataKey);
  }

  /// Register with Email and Password
  Future<bool> registerWithEmail({
    required String name,
    required String email,
    required String password,
    String? profilePictureUrl,
    String? mobileNumber,
    bool isPublisher = false,
    Map<String, dynamic>? additionalDetails,
  }) async {
    try {
      UserCredential userCredential =
          await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );
      final user = userCredential.user!;

      await user.sendEmailVerification();

      Map<String, dynamic> userData = {
        'uid': user.uid,
        'name': name,
        'email': email,
        'emailVerified': user.emailVerified,
        'mobileNumber': mobileNumber ?? '',
        'profilePictureUrl': profilePictureUrl ?? '',
        'isPublisher': isPublisher,
        'createdAt': FieldValue.serverTimestamp(),
        'lastLogin': FieldValue.serverTimestamp(),
        'additionalDetails': additionalDetails ?? {},
      };

      await _firestoreService.setDocument(
        collectionPath: 'users',
        documentId: user.uid,
        data: userData,
      );

      userData['createdAt'] = DateTime.now().toIso8601String();
      userData['lastLogin'] = DateTime.now().toIso8601String();
      await _saveUserDataToPrefs(userData);

      // Send welcome email
      try {
        final userModel = UserModel.fromMap(userData, user.uid);
        await _emailService.sendUserRegistrationEmail(userModel);
      } catch (emailError) {
        // Don't fail registration if email fails
        print("Error sending welcome email: $emailError");
      }

      return true;
    } catch (e) {
      print("Registration error: $e");
      return false;
    }
  }

  /// Login with Email and Password
  Future<bool> loginWithEmail(String email, String password) async {
    try {
      UserCredential userCredential = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );
      final user = userCredential.user!;

      final userData = await _firestoreService.getDocument(
        collectionPath: 'users',
        documentId: user.uid,
      );

      if (userData != null) {
        userData['lastLogin'] = FieldValue.serverTimestamp();
        userData['emailVerified'] = user.emailVerified;

        await _firestoreService.updateDocument(
          collectionPath: 'users',
          documentId: user.uid,
          data: {
            'lastLogin': userData['lastLogin'],
            'emailVerified': userData['emailVerified'],
          },
        );

        userData['lastLogin'] = DateTime.now().toIso8601String();
        userData['createdAt'] =
            (userData['createdAt'] as Timestamp?)?.toDate().toIso8601String();
        await _saveUserDataToPrefs(userData);
      }

      return true;
    } catch (e) {
      print("Login error: $e");
      return false;
    }
  }

  /// Sign in with Google - Only fetch Google data on first sign-in
  Future<bool> signInWithGoogle() async {
    try {
      UserCredential userCredential;

      if (kIsWeb) {
        GoogleAuthProvider googleProvider = GoogleAuthProvider();
        googleProvider.addScope('email');
        googleProvider.addScope('profile');

        try {
          userCredential = await _auth.signInWithPopup(googleProvider);
        } catch (popupError) {
          print('Popup failed: $popupError');
          await _auth.signInWithRedirect(googleProvider);
          final result = await _auth.getRedirectResult();
          if (result.credential == null) return false;
          userCredential = result;
        }
      } else {
        final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();
        if (googleUser == null) return false;

        final GoogleSignInAuthentication googleAuth =
            await googleUser.authentication;
        final OAuthCredential credential = GoogleAuthProvider.credential(
          accessToken: googleAuth.accessToken,
          idToken: googleAuth.idToken,
        );

        userCredential = await _auth.signInWithCredential(credential);
      }

      final user = userCredential.user!;

      // Check if user already exists in Firestore
      final existingUserData = await _firestoreService.getDocument(
        collectionPath: 'users',
        documentId: user.uid,
      );

      Map<String, dynamic> userData;

      if (existingUserData == null) {
        // First-time sign-in: Use Google data to create user document
        userData = {
          'uid': user.uid,
          'name': user.displayName ?? '',
          'email': user.email ?? '',
          'emailVerified': user.emailVerified,
          'mobileNumber': '',
          'profilePictureUrl': user.photoURL ?? '',
          'isPublisher': false,
          'createdAt': FieldValue.serverTimestamp(),
          'lastLogin': FieldValue.serverTimestamp(),
          'additionalDetails': {'provider': 'google'},
        };

        await _firestoreService.setDocument(
          collectionPath: 'users',
          documentId: user.uid,
          data: userData,
        );
      } else {
        // Subsequent sign-in: Use existing Firestore data and update only necessary fields
        userData = existingUserData;
        userData['lastLogin'] = FieldValue.serverTimestamp();
        userData['emailVerified'] = user.emailVerified;

        await _firestoreService.updateDocument(
          collectionPath: 'users',
          documentId: user.uid,
          data: {
            'lastLogin': userData['lastLogin'],
            'emailVerified': userData['emailVerified'],
          },
        );
      }

      // Prepare data for SharedPreferences
      userData['lastLogin'] = DateTime.now().toIso8601String();
      userData['createdAt'] =
          (userData['createdAt'] as Timestamp?)?.toDate().toIso8601String() ??
              DateTime.now().toIso8601String();
      await _saveUserDataToPrefs(userData);

      return true;
    } catch (e) {
      print("Sign-in error: $e");
      if (e is FirebaseAuthException) {
        print("Code: ${e.code}");
        print("Message: ${e.message}");
      }
      return false;
    }
  }

  /// Sign out
  Future<void> signOut() async {
    await _auth.signOut();
    await _googleSignIn.signOut();
    await _clearUserDataFromPrefs();
  }

  /// Get current user data from SharedPreferences (fallback to Firestore)
  Future<Map<String, dynamic>?> getUserData() async {
    if (!isLoggedIn) return null;

    Map<String, dynamic>? userData = await _getUserDataFromPrefs();
    if (userData != null) return userData;

    userData = await _firestoreService.getDocument(
      collectionPath: 'users',
      documentId: _auth.currentUser!.uid,
    );
    if (userData != null) {
      userData['createdAt'] =
          (userData['createdAt'] as Timestamp?)?.toDate().toIso8601String();
      userData['lastLogin'] =
          (userData['lastLogin'] as Timestamp?)?.toDate().toIso8601String();
      await _saveUserDataToPrefs(userData);
    }
    return userData;
  }

  /// Stream current user data from Firestore (and update SharedPreferences)
  Stream<Map<String, dynamic>?> streamUserData() {
    if (!isLoggedIn) return Stream.value(null);
    return _firestoreService
        .streamDocument(
      collectionPath: 'users',
      documentId: _auth.currentUser!.uid,
    )
        .map((snapshot) {
      if (!snapshot.exists) return null;
      final userData = snapshot.data()!;
      userData['createdAt'] =
          (userData['createdAt'] as Timestamp?)?.toDate().toIso8601String();
      userData['lastLogin'] =
          (userData['lastLogin'] as Timestamp?)?.toDate().toIso8601String();
      _saveUserDataToPrefs(userData);
      return userData;
    });
  }

  /// Update user profile
  Future<bool> updateUserProfile({
    String? name,
    String? profilePictureUrl,
    String? mobileNumber,
    bool? isPublisher,
    Map<String, dynamic>? additionalDetails,
  }) async {
    if (!isLoggedIn) return false;

    try {
      Map<String, dynamic> updateData = {};
      if (name != null) updateData['name'] = name;
      if (profilePictureUrl != null) {
        updateData['profilePictureUrl'] = profilePictureUrl;
      }
      if (mobileNumber != null) updateData['mobileNumber'] = mobileNumber;
      if (isPublisher != null) updateData['isPublisher'] = isPublisher;
      if (additionalDetails != null) {
        updateData['additionalDetails'] = additionalDetails;
      }
      updateData['updatedAt'] = FieldValue.serverTimestamp();
      updateData['emailVerified'] = _auth.currentUser!.emailVerified;

      await _firestoreService.updateDocument(
        collectionPath: 'users',
        documentId: _auth.currentUser!.uid,
        data: updateData,
      );

      final userData = await getUserData();
      if (userData != null) {
        updateData.forEach((key, value) {
          if (key != 'updatedAt') userData[key] = value;
          if (key == 'updatedAt') {
            userData[key] = DateTime.now().toIso8601String();
          }
        });
        await _saveUserDataToPrefs(userData);
      }

      return true;
    } catch (e) {
      print("Update profile error: $e");
      return false;
    }
  }

  /// Check if current user is a publisher
  Future<bool> isCurrentUserPublisher() async {
    final userData = await getUserData();
    return userData?['isPublisher'] ?? false;
  }

  /// Check if email is verified
  Future<bool> isEmailVerified() async {
    if (!isLoggedIn) return false;
    await _auth.currentUser!.reload();
    final userData = await getUserData();
    if (userData != null) {
      userData['emailVerified'] = _auth.currentUser!.emailVerified;
      await _saveUserDataToPrefs(userData);
    }
    return _auth.currentUser!.emailVerified;
  }

  /// Send email verification
  Future<bool> sendEmailVerification() async {
    if (!isLoggedIn) return false;
    try {
      await _auth.currentUser!.sendEmailVerification();

      return true;
    } catch (e) {
      print("Email verification error: $e");
      return false;
    }
  }

  /// Update mobile number
  Future<bool> updateMobileNumber(String mobileNumber) async {
    return await updateUserProfile(mobileNumber: mobileNumber);
  }
}
