import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:guest_posts_buyer/core/models/email_template_model.dart';
import 'package:guest_posts_buyer/core/services/cRUD_services.dart';

class EmailTemplateService {
  static final EmailTemplateService _instance =
      EmailTemplateService._internal();
  factory EmailTemplateService() => _instance;
  EmailTemplateService._internal();

  final FirestoreService _firestoreService = FirestoreService();

  // Create or update email template
  Future<String> saveTemplate(EmailTemplateModel template) async {
    if (template.templateId != null) {
      await _firestoreService.updateDocument(
        collectionPath: 'email_templates',
        documentId: template.templateId!,
        data: template.toMap(),
      );
      return template.templateId!;
    } else {
      return await _firestoreService.addDocument(
        collectionPath: 'email_templates',
        data: template.toMap(),
      );
    }
  }

  // Get template by type
  Future<EmailTemplateModel?> getTemplateByType(EmailTemplateType type) async {
    try {
      final querySnapshot = await _firestoreService.getCollection(
        collectionPath: 'email_templates',
        queryBuilder: (query) => query
            .where('type', isEqualTo: type.toString())
            .where('isActive', isEqualTo: true)
            .limit(1),
      );

      if (querySnapshot.isNotEmpty) {
        final doc = querySnapshot.first;
        return EmailTemplateModel.fromMap(doc..['templateId'] = doc['id']);
      }
      return null;
    } catch (e) {
      print('Error getting template by type: $e');
      return null;
    }
  }

  // Get all templates
  Future<List<EmailTemplateModel>> getAllTemplates() async {
    try {
      final querySnapshot = await _firestoreService.getCollection(
        collectionPath: 'email_templates',
        queryBuilder: (query) => query.orderBy('name'),
      );

      return querySnapshot.map((doc) {
        return EmailTemplateModel.fromMap(doc..['templateId'] = doc['id']);
      }).toList();
    } catch (e) {
      print('Error getting all templates: $e');
      return [];
    }
  }

  // Delete template
  Future<void> deleteTemplate(String templateId) async {
    await _firestoreService.deleteDocument(
      collectionPath: 'email_templates',
      documentId: templateId,
    );
  }

  // Initialize default templates
  Future<void> initializeDefaultTemplates() async {
    final defaultTemplates = _getDefaultTemplates();

    for (final template in defaultTemplates) {
      final existing = await getTemplateByType(template.type);
      if (existing == null) {
        await saveTemplate(template);
      }
    }
  }

  // Get default email templates
  List<EmailTemplateModel> _getDefaultTemplates(  ) {
    final now = Timestamp.now();

    return [
      // Order Confirmation Template
      EmailTemplateModel(
        type: EmailTemplateType.orderConfirmation,
        name: 'Order Confirmation',
        subject: 'Order Confirmation - {{orderNumber}}',
        htmlContent: '''
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Order Confirmation</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #1A73E8; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; background: #f9f9f9; }
        .order-details { background: white; padding: 15px; margin: 15px 0; border-radius: 5px; }
        .footer { text-align: center; padding: 20px; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Order Confirmation</h1>
        </div>
        <div class="content">
            <p>Dear {{buyerName}},</p>
            <p>Thank you for your order! We have received your guest post request and it is being processed.</p>

            <div class="order-details">
                <h3>Order Details</h3>
                <p><strong>Order Number:</strong> {{orderNumber}}</p>
                <p><strong>Website:</strong> {{websiteUrl}}</p>
                <p><strong>Post Title:</strong> {{postTitle}}</p>
                <p><strong>Word Count:</strong> {{wordCount}}</p>
                <p><strong>Total Amount:</strong> ${{totalPrice}}</p>
                <p><strong>Order Date:</strong> {{orderDate}}</p>
            </div>

            <p>You will receive updates as your order progresses through our system.</p>
        </div>
        <div class="footer">
            <p>Thank you for choosing our service!</p>
        </div>
    </div>
</body>
</html>
        ''',
        textContent: '''
Order Confirmation - {{orderNumber}}

Dear {{buyerName}},

Thank you for your order! We have received your guest post request and it is being processed.

Order Details:
- Order Number: {{orderNumber}}
- Website: {{websiteUrl}}
- Post Title: {{postTitle}}
- Word Count: {{wordCount}}
- Total Amount: ${{totalPrice}}
- Order Date: {{orderDate}}

You will receive updates as your order progresses through our system.

Thank you for choosing our service!
        ''',
        variables: {
          'buyerName': 'Buyer\'s name',
          'orderNumber': 'Order ID',
          'websiteUrl': 'Website URL',
          'postTitle': 'Post title',
          'wordCount': 'Word count',
          'totalPrice': 'Total price',
          'orderDate': 'Order date',
        },
        createdAt: now,
        lastUpdated: now,
      ),

      // Order Status Update Template
      EmailTemplateModel(
        type: EmailTemplateType.orderStatusUpdate,
        name: 'Order Status Update',
        subject: 'Order Update - {{orderNumber}} is now {{status}}',
        htmlContent: '''
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Order Status Update</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #1A73E8; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; background: #f9f9f9; }
        .status-update { background: white; padding: 15px; margin: 15px 0; border-radius: 5px; border-left: 4px solid #1A73E8; }
        .footer { text-align: center; padding: 20px; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Order Status Update</h1>
        </div>
        <div class="content">
            <p>Dear {{buyerName}},</p>
            <p>Your order status has been updated.</p>

            <div class="status-update">
                <h3>Status Update</h3>
                <p><strong>Order Number:</strong> {{orderNumber}}</p>
                <p><strong>New Status:</strong> {{status}}</p>
                <p><strong>Status Detail:</strong> {{statusDetail}}</p>
                <p><strong>Updated Date:</strong> {{updateDate}}</p>
                {{#notes}}
                <p><strong>Notes:</strong> {{notes}}</p>
                {{/notes}}
            </div>

            <p>Thank you for your patience.</p>
        </div>
        <div class="footer">
            <p>Thank you for choosing our service!</p>
        </div>
    </div>
</body>
</html>
        ''',
        textContent: '''
Order Status Update - {{orderNumber}} is now {{status}}

Dear {{buyerName}},

Your order status has been updated.

Status Update:
- Order Number: {{orderNumber}}
- New Status: {{status}}
- Status Detail: {{statusDetail}}
- Updated Date: {{updateDate}}
{{#notes}}
- Notes: {{notes}}
{{/notes}}

Thank you for your patience.

Thank you for choosing our service!
        ''',
        variables: {
          'buyerName': 'Buyer\'s name',
          'orderNumber': 'Order ID',
          'status': 'Order status',
          'statusDetail': 'Status detail',
          'updateDate': 'Update date',
          'notes': 'Additional notes',
        },
        createdAt: now,
        lastUpdated: now,
      ),

      // Publisher Notification Template
      EmailTemplateModel(
        type: EmailTemplateType.publisherNotification,
        name: 'Publisher Notification',
        subject: 'New Order Assignment - {{orderNumber}}',
        htmlContent: '''
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>New Order Assignment</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #1A73E8; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; background: #f9f9f9; }
        .order-details { background: white; padding: 15px; margin: 15px 0; border-radius: 5px; }
        .footer { text-align: center; padding: 20px; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>New Order Assignment</h1>
        </div>
        <div class="content">
            <p>Dear {{publisherName}},</p>
            <p>You have been assigned a new guest post order. Please review the details below:</p>

            <div class="order-details">
                <h3>Order Details</h3>
                <p><strong>Order Number:</strong> {{orderNumber}}</p>
                <p><strong>Website:</strong> {{websiteUrl}}</p>
                <p><strong>Post Title:</strong> {{postTitle}}</p>
                <p><strong>Word Count:</strong> {{wordCount}}</p>
                <p><strong>Backlink Type:</strong> {{backlinkType}}</p>
                <p><strong>Sponsored:</strong> {{isSponsored}}</p>
                <p><strong>Total Amount:</strong> ${{totalPrice}}</p>
            </div>

            <p>Please log in to your dashboard to view the complete order details and begin working on it.</p>
        </div>
        <div class="footer">
            <p>Thank you for your partnership!</p>
        </div>
    </div>
</body>
</html>
        ''',
        textContent: '''
New Order Assignment - {{orderNumber}}

Dear {{publisherName}},

You have been assigned a new guest post order. Please review the details below:

Order Details:
- Order Number: {{orderNumber}}
- Website: {{websiteUrl}}
- Post Title: {{postTitle}}
- Word Count: {{wordCount}}
- Backlink Type: {{backlinkType}}
- Sponsored: {{isSponsored}}
- Total Amount: ${{totalPrice}}

Please log in to your dashboard to view the complete order details and begin working on it.

Thank you for your partnership!
        ''',
        variables: {
          'publisherName': 'Publisher\'s name',
          'publisherEmail': 'Publisher\'s email',
          'orderNumber': 'Order ID',
          'websiteUrl': 'Website URL',
          'postTitle': 'Post title',
          'wordCount': 'Word count',
          'backlinkType': 'Backlink type',
          'isSponsored': 'Sponsored status',
          'totalPrice': 'Total price',
        },
        createdAt: now,
        lastUpdated: now,
      ),

      // Payment Confirmation Template
      EmailTemplateModel(
        type: EmailTemplateType.paymentConfirmation,
        name: 'Payment Confirmation',
        subject: 'Payment Confirmation - ${{totalPrice}}',
        htmlContent: '''
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Payment Confirmation</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #34A853; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; background: #f9f9f9; }
        .payment-details { background: white; padding: 15px; margin: 15px 0; border-radius: 5px; }
        .footer { text-align: center; padding: 20px; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Payment Confirmation</h1>
        </div>
        <div class="content">
            <p>Dear {{userName}},</p>
            <p>Your payment has been successfully processed!</p>

            <div class="payment-details">
                <h3>Payment Details</h3>
                <p><strong>Amount:</strong> ${{totalPrice}}</p>
                <p><strong>Payment ID:</strong> {{paymentId}}</p>
                <p><strong>Order ID:</strong> {{orderId}}</p>
                <p><strong>Payment Date:</strong> {{paymentDate}}</p>
            </div>

            <p>Thank you for your payment. You will receive updates on your order progress.</p>
        </div>
        <div class="footer">
            <p>Thank you for choosing our service!</p>
        </div>
    </div>
</body>
</html>
        ''',
        textContent: '''
Payment Confirmation - ${{totalPrice}}

Dear {{userName}},

Your payment has been successfully processed!

Payment Details:
- Amount: ${{totalPrice}}
- Payment ID: {{paymentId}}
- Order ID: {{orderId}}
- Payment Date: {{paymentDate}}

Thank you for your payment. You will receive updates on your order progress.

Thank you for choosing our service!
        ''',
        variables: {
          'userName': 'User\'s name',
          'userEmail': 'User\'s email',
          'totalPrice': 'Payment amount',
          'paymentId': 'Payment ID',
          'orderId': 'Order ID',
          'paymentDate': 'Payment date',
        },
        createdAt: now,
        lastUpdated: now,
      ),
    ];
  }
}




