import 'package:cloud_firestore/cloud_firestore.dart';

class EmailConfigModel {
  final String? configId;
  final String smtpHost;
  final int smtpPort;
  final String username;
  final String password;
  final String fromEmail;
  final String fromName;
  final bool useTLS;
  final bool useSSL;
  final bool isActive;
  final String environment; // 'development', 'production'
  final Timestamp createdAt;
  final Timestamp lastUpdated;

  EmailConfigModel({
    this.configId,
    required this.smtpHost,
    required this.smtpPort,
    required this.username,
    required this.password,
    required this.fromEmail,
    required this.fromName,
    this.useTLS = true,
    this.useSSL = false,
    this.isActive = true,
    required this.environment,
    required this.createdAt,
    required this.lastUpdated,
  });

  factory EmailConfigModel.fromMap(Map<String, dynamic> map) {
    return EmailConfigModel(
      configId: map['configId'] as String?,
      smtpHost: map['smtpHost'] as String? ?? '',
      smtpPort: map['smtpPort'] as int? ?? 587,
      username: map['username'] as String? ?? '',
      password: map['password'] as String? ?? '',
      fromEmail: map['fromEmail'] as String? ?? '',
      fromName: map['fromName'] as String? ?? '',
      useTLS: map['useTLS'] as bool? ?? true,
      useSSL: map['useSSL'] as bool? ?? false,
      isActive: map['isActive'] as bool? ?? true,
      environment: map['environment'] as String? ?? 'development',
      createdAt: map['createdAt'] as Timestamp? ?? Timestamp.now(),
      lastUpdated: map['lastUpdated'] as Timestamp? ?? Timestamp.now(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'configId': configId,
      'smtpHost': smtpHost,
      'smtpPort': smtpPort,
      'username': username,
      'password': password,
      'fromEmail': fromEmail,
      'fromName': fromName,
      'useTLS': useTLS,
      'useSSL': useSSL,
      'isActive': isActive,
      'environment': environment,
      'createdAt': createdAt,
      'lastUpdated': lastUpdated,
    };
  }

  EmailConfigModel copyWith({
    String? configId,
    String? smtpHost,
    int? smtpPort,
    String? username,
    String? password,
    String? fromEmail,
    String? fromName,
    bool? useTLS,
    bool? useSSL,
    bool? isActive,
    String? environment,
    Timestamp? createdAt,
    Timestamp? lastUpdated,
  }) {
    return EmailConfigModel(
      configId: configId ?? this.configId,
      smtpHost: smtpHost ?? this.smtpHost,
      smtpPort: smtpPort ?? this.smtpPort,
      username: username ?? this.username,
      password: password ?? this.password,
      fromEmail: fromEmail ?? this.fromEmail,
      fromName: fromName ?? this.fromName,
      useTLS: useTLS ?? this.useTLS,
      useSSL: useSSL ?? this.useSSL,
      isActive: isActive ?? this.isActive,
      environment: environment ?? this.environment,
      createdAt: createdAt ?? this.createdAt,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  // Default configurations for different providers
  static EmailConfigModel gmail({
    required String username,
    required String password,
    required String fromEmail,
    required String fromName,
    String environment = 'development',
  }) {
    return EmailConfigModel(
      smtpHost: 'smtp.gmail.com',
      smtpPort: 587,
      username: username,
      password: password,
      fromEmail: fromEmail,
      fromName: fromName,
      useTLS: true,
      useSSL: false,
      environment: environment,
      createdAt: Timestamp.now(),
      lastUpdated: Timestamp.now(),
    );
  }

  static EmailConfigModel sendGrid({
    required String apiKey,
    required String fromEmail,
    required String fromName,
    String environment = 'development',
  }) {
    return EmailConfigModel(
      smtpHost: 'smtp.sendgrid.net',
      smtpPort: 587,
      username: 'apikey',
      password: apiKey,
      fromEmail: fromEmail,
      fromName: fromName,
      useTLS: true,
      useSSL: false,
      environment: environment,
      createdAt: Timestamp.now(),
      lastUpdated: Timestamp.now(),
    );
  }

  static EmailConfigModel outlook({
    required String username,
    required String password,
    required String fromEmail,
    required String fromName,
    String environment = 'development',
  }) {
    return EmailConfigModel(
      smtpHost: 'smtp-mail.outlook.com',
      smtpPort: 587,
      username: username,
      password: password,
      fromEmail: fromEmail,
      fromName: fromName,
      useTLS: true,
      useSSL: false,
      environment: environment,
      createdAt: Timestamp.now(),
      lastUpdated: Timestamp.now(),
    );
  }
}
