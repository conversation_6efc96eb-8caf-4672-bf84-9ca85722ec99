import 'package:easy_sidemenu/easy_sidemenu.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:go_router/go_router.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:guest_posts_buyer/core/services/auth_service.dart';
import 'package:guest_posts_buyer/core/utils/colors.dart';

import 'package:guest_posts_buyer/features/admin_dashboard/add_website.dart';
import 'package:guest_posts_buyer/features/admin_dashboard/dashboard.dart';
import 'package:guest_posts_buyer/features/admin_dashboard/email_settings_page.dart';
import 'package:guest_posts_buyer/features/admin_dashboard/invoices_page/admin_invoices_page.dart';
import 'package:guest_posts_buyer/features/admin_dashboard/landing_data.dart';
import 'package:guest_posts_buyer/features/admin_dashboard/offers_screen.dart';
import 'package:guest_posts_buyer/features/admin_dashboard/policies_page.dart';
import 'package:guest_posts_buyer/features/admin_dashboard/messaging.dart';
import 'package:guest_posts_buyer/features/admin_dashboard/orders_screen.dart';
import 'package:guest_posts_buyer/features/admin_dashboard/payment_page.dart';
import 'package:guest_posts_buyer/features/admin_dashboard/settings_page.dart';
import 'package:guest_posts_buyer/features/admin_dashboard/support_page.dart';
import 'package:guest_posts_buyer/features/admin_dashboard/users_screen.dart';
import 'package:guest_posts_buyer/features/admin_dashboard/websites_screen.dart';

class BaseScreen extends StatefulWidget {
  final String initialPage;
  final String arg;
  const BaseScreen({super.key, this.initialPage = '', this.arg = ''});

  @override
  State<BaseScreen> createState() => _BaseScreenState();
}

class _BaseScreenState extends State<BaseScreen> {
  final SideMenuController sideMenu = SideMenuController();
  bool _sideToggle = false;
  final AuthService _authService = AuthService();

  final Map<String, int> _pageToIndex = {
    'dashboard': 0,
    'users': 1,
    'websites': 2,
    'orders': 3,
    'earnings': 4,
    'support': 5,
    'settings': 6,
    'email-settings': 7,
    'invoices': 8,
    'notification': 9,
    'landing': 10,
    'offers': 11,
    'policies': 12,
    'add-website': 13,
  };

  @override
  void initState() {
    super.initState();
    final initialIndex = _pageToIndex[widget.initialPage] ?? 0;
    sideMenu.changePage(initialIndex);
  }

  @override
  void dispose() {
    sideMenu.dispose();
    super.dispose();
  }

  void _navigateToPage(String page) {
    context.go('/$page');
  }

  @override
  Widget build(BuildContext context) {
    double width = MediaQuery.sizeOf(context).width;

    return Scaffold(
      backgroundColor: Colors.white,
      body: Row(
        children: [
          if (width >= 600)
            Container(
              decoration: BoxDecoration(color: AppColors.backgroundColor),
              //  padding: const EdgeInsets.symmetric(vertical: 18, horizontal: 16),
              child: InkWell(
                mouseCursor: MouseCursor.defer,
                child: SideMenu(
                  controller: sideMenu,
                  showToggle: false,
                  alwaysShowFooter: true,
                  style: SideMenuStyle(
                    itemBorderRadius: BorderRadius.circular(12),
                    showTooltip: true,
                    displayMode: _sideToggle
                        ? SideMenuDisplayMode.compact
                        : SideMenuDisplayMode.open,
                    backgroundColor: AppColors.backgroundColor,
                    showHamburger: false,
                    iconSize: 25,
                    iconSizeExpandable: 30,
                    unselectedIconColorExpandable:
                        const Color.fromARGB(75, 255, 255, 255),
                    unselectedIconColor:
                        const Color.fromARGB(255, 0, 0, 0).withOpacity(0.5),
                    selectedIconColorExpandable: AppColors.primaryBlue,
                    unselectedTitleTextStyle: GoogleFonts.poppins(
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                        color: Color.fromARGB(74, 0, 0, 0)),
                    itemHeight: 50,
                    openSideMenuWidth: 265,
                    hoverColor: AppColors.componentBackColor,
                    // itemOuterPadding: EdgeInsets.all(3),
                    selectedHoverColor:
                        const Color.fromARGB(255, 126, 126, 126),
                    selectedColor: AppColors.primaryBlue,
                    selectedTitleTextStyle: GoogleFonts.poppins(
                        fontWeight: FontWeight.w600,
                        fontSize: 16,
                        color: Color.fromARGB(255, 255, 255, 255)),
                    selectedIconColor: const Color.fromARGB(255, 255, 255, 255),
                    arrowCollapse: const Color.fromARGB(255, 255, 255, 255),
                    arrowOpen: const Color.fromARGB(255, 255, 255, 255),
                    toggleColor: const Color.fromARGB(255, 255, 255, 255),
                  ),
                  title: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Center(
                      child: Column(
                        children: [
                          Text(
                            '\nGuest',
                            style: GoogleFonts.poppins(
                                fontSize: _sideToggle ? 14 : 44,
                                fontWeight: FontWeight.bold,
                                color: const Color.fromARGB(255, 0, 0, 0),
                                height: 1),
                          ),
                          Text(
                            'Posts\n',
                            style: GoogleFonts.poppins(
                                fontSize: _sideToggle ? 14 : 44,
                                fontWeight: FontWeight.bold,
                                color: AppColors.primaryBlue,
                                height: 1),
                          ),
                        ],
                      ),
                    ),
                  ),
                  footer: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: InkWell(
                      onTap: () {
                        setState(() {
                          _sideToggle = !_sideToggle;
                        });
                      },
                      child: Container(
                        decoration: BoxDecoration(
                          // color: AppTheme.accentColor,
                          borderRadius: BorderRadius.circular(50),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.symmetric(
                              vertical: 10, horizontal: 10),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                  _sideToggle
                                      ? CupertinoIcons.forward
                                      : CupertinoIcons.back,
                                  color: AppColors.primaryBlue),
                              if (!_sideToggle)
                                Text(
                                  ' Collapse',
                                  style:
                                      TextStyle(color: AppColors.primaryBlue),
                                ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                  items: [
                    SideMenuItem(
                      title: 'Dashboard',
                      onTap: (index, _) => _navigateToPage('dashboard'),
                      icon: const Icon(Icons.web),
                      tooltipContent: "Dashboard",
                    ),
                    SideMenuItem(
                      title: 'Users',
                      onTap: (index, _) => _navigateToPage('users'),
                      icon: const Icon(
                        CupertinoIcons.person,
                        size: 1,
                      ),
                    ),
                    SideMenuItem(
                      title: 'Websites',
                      onTap: (index, _) => _navigateToPage('websites'),
                      icon: const Icon(CupertinoIcons.link),
                      // badgeContent: const Text('3',
                      //     style: TextStyle(color: Colors.white)),
                    ),
                    SideMenuItem(
                      title: 'Orders',
                      onTap: (index, _) => _navigateToPage('orders'),
                      icon: const Icon(FontAwesomeIcons.listCheck),
                    ),
                    SideMenuItem(
                      title: 'earnings',
                      onTap: (index, _) => _navigateToPage('earnings'),
                      icon: const Icon(FontAwesomeIcons.dollarSign),
                    ),
                    SideMenuItem(
                      title: 'Support',
                      onTap: (index, _) => _navigateToPage('support'),
                      icon: const Icon(CupertinoIcons.question_diamond),
                    ),
                    SideMenuItem(
                      title: 'Settings',
                      onTap: (index, _) => _navigateToPage('settings'),
                      icon: const Icon(FontAwesomeIcons.gear),
                    ),
                    SideMenuItem(
                      title: 'Email Settings',
                      onTap: (index, _) => _navigateToPage('email-settings'),
                      icon: const Icon(Icons.email_outlined),
                    ),
                    SideMenuItem(
                      title: 'Invoices',
                      onTap: (index, _) => _navigateToPage('invoices'),
                      icon: const Icon(FontAwesomeIcons.fileInvoice),
                    ),
                    SideMenuItem(
                      title: 'Send Notification',
                      onTap: (index, _) => _navigateToPage('notification'),
                      icon: const Icon(Icons.notification_add_outlined),
                    ),
                    SideMenuItem(
                      title: 'Landing Page',
                      onTap: (index, _) => _navigateToPage('landing'),
                      icon: const Icon(CupertinoIcons.rocket),
                    ),
                    SideMenuItem(
                      title: 'Offers',
                      onTap: (index, _) => _navigateToPage('offers'),
                      icon: const Icon(FontAwesomeIcons.tag),
                    ),
                    SideMenuItem(
                      title: 'Policies',
                      onTap: (index, _) => _navigateToPage('policies'),
                      icon: const Icon(FontAwesomeIcons.fileContract),
                    ),
                  ],
                ),
              ),
            ),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 16.0),
              child: Column(
                children: [
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: StreamBuilder<Map<String, dynamic>?>(
                      stream: _authService.streamUserData(),
                      builder: (context, snapshot) {
                        final userData = snapshot.data ?? {};

                        return Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Spacer(),
                            Row(
                              children: [
                                Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    CircleAvatar(
                                      backgroundColor:
                                          Colors.amber.withAlpha(51),
                                      radius: 20,
                                      backgroundImage:
                                          userData['profilePictureUrl']
                                                      ?.isNotEmpty ??
                                                  false
                                              ? NetworkImage(
                                                  userData['profilePictureUrl'])
                                              : null,
                                      child: userData['profilePictureUrl']
                                                  ?.isEmpty ??
                                              true
                                          ? const Icon(
                                              Icons.person,
                                              color: Colors.amber,
                                            )
                                          : null,
                                    ),
                                    const SizedBox(width: 10),
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          userData['name'] ?? 'User',
                                          style: const TextStyle(
                                            fontFamily: 'Alatsi',
                                            fontWeight: FontWeight.bold,
                                            fontSize: 16,
                                            color: Colors.black87,
                                            height: 1,
                                          ),
                                        ),
                                        Text(
                                          'Admin',
                                          style: const TextStyle(
                                              fontFamily: 'Alatsi'),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                                SizedBox(
                                  width: 16,
                                ),
                                Container(
                                  width: 2,
                                  height: 45,
                                  margin:
                                      const EdgeInsets.symmetric(vertical: 5),
                                  decoration: BoxDecoration(
                                    color: const Color.fromARGB(
                                        255, 218, 218, 218),
                                  ),
                                ),
                                SizedBox(
                                  width: 16,
                                ),
                                Row(
                                  children: [
                                    InkWell(
                                      onTap: () => setState(() {
                                        AuthService().signOut();
                                      }),
                                      child: CircleAvatar(
                                        backgroundColor:
                                            Colors.red.withAlpha(51),
                                        child: Icon(Icons.logout,
                                            color: Colors.redAccent, size: 20),
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ],
                        );
                      },
                    ),
                  ),
                  Expanded(
                    child: RouteAwarePageView(
                        pageToIndex: _pageToIndex,
                        sideMenuController: sideMenu,
                        arg: widget.arg),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class RouteAwarePageView extends StatefulWidget {
  final Map<String, int> pageToIndex;
  final SideMenuController sideMenuController;
  final String arg;
  const RouteAwarePageView(
      {super.key,
      required this.pageToIndex,
      required this.sideMenuController,
      required this.arg});

  @override
  State<RouteAwarePageView> createState() => _RouteAwarePageViewState();
}

class _RouteAwarePageViewState extends State<RouteAwarePageView> {
  late PageController _pageController;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final router = GoRouter.of(context);
      final currentPage =
          router.routeInformationProvider.value.uri.pathSegments.last;
      final initialIndex = widget.pageToIndex[currentPage] ?? 0;
      if (_pageController.hasClients) {
        _pageController.jumpToPage(initialIndex);
      }
      widget.sideMenuController.changePage(initialIndex);
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<RouteInformation>(
      valueListenable: GoRouter.of(context).routeInformationProvider,
      builder: (context, routeInfo, child) {
        final currentPage = routeInfo.uri.pathSegments.last;
        final currentIndex = widget.pageToIndex[currentPage] ?? 0;

        if (_pageController.hasClients &&
            _pageController.page?.round() != currentIndex) {
          _pageController.jumpToPage(currentIndex);
        }
        if (widget.sideMenuController.currentPage != currentIndex) {
          widget.sideMenuController.changePage(currentIndex);
        }

        return PageView(
          controller: _pageController,
          physics: const NeverScrollableScrollPhysics(),
          children: [
            DashboardScreen(),
            UserManagementScreen(),
            WebsiteManagementScreen(),
            OrderManagementScreen(),
            PaymentsWithdrawalsScreen(),
            AdminSupportPage(),
            AdminSettingsPage(),
            EmailSettingsPage(),
            AdminInvoicesPage(),
            MessagingPage(),
            LandingDataPage(),
            OffersManagementScreen(),
            PoliciesManagementPage(),
            AddWebsitePage(),

            // BuyPostPage(
            //   id: widget.arg,
            // )
          ],
        );
      },
    );
  }
}

// Placeholder widget (replace with your actual implementation)
